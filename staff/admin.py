from django.contrib import admin
from .models import Profile, Product, Service, Gallery, Contact, Video, Qrcode
from import_export.admin import ImportExportMixin


class ProfileAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'position', 'phone', 'email', 'website')

class ProductsAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'image', 'website')

class ServicesAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'image')

class ContactAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'email', 'message')

class QrcodeAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'link', 'code')

class GalleryAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'image',)

admin.site.register(Product,ProductsAdmin)
admin.site.register(Profile,ProfileAdmin)
admin.site.register(Service,ServicesAdmin)
admin.site.register(Gallery, GalleryAdmin)
admin.site.register(Contact,ContactAdmin)
admin.site.register(Video)
admin.site.register(Qrcode, QrcodeAdmin)




