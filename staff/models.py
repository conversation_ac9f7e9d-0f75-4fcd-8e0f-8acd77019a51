from django.db import models
from django.db.models.signals import pre_save
from django.dispatch import receiver
from io import BytesIO
from PIL import Image
import os
import qrcode
from django.core.files import File

class Profile(models.Model):
    image = models.FileField(upload_to='Profile')
    name = models.CharField(max_length=50)
    position = models.CharField(max_length=50)
    phone = models.CharField(max_length=15)
    whatsapp = models.CharField(max_length=15)
    email = models.EmailField()
    map = models.URLField()
    website = models.URLField()
    about = models.TextField()
    slug = models.SlugField(max_length=200, unique=True, null=True, blank=True)

    def __str__(self):
        return self.name

class Product(models.Model):
    order = models.CharField(max_length=50)
    name = models.CharField(max_length=50)
    subtitle = models.CharField(max_length=50)
    image = models.ImageField(upload_to='Product')
    website = models.URLField()
    facebook = models.URLField(null=True, blank=True)
    instagram = models.URLField(null=True, blank=True)
    linkedin = models.URLField(null=True, blank=True)
    youtube = models.URLField(null=True, blank=True)
    tiktok = models.URLField(null=True, blank=True)

    def __str__(self):
        return self.name


class Service(models.Model):
    name = models.CharField(max_length=50)
    image = models.ImageField(upload_to='Service')
    description = models.TextField()

    def __str__(self):
        return self.name

class Gallery(models.Model):
    name = models.CharField(max_length=50)
    image = models.ImageField(upload_to='Gallery')

    def __str__(self):
        return self.name

class Video(models.Model):
    title = models.CharField(max_length=255)
    youtube_video_id = models.CharField(max_length=20)  # Assuming YouTube video IDs are less than 20 characters

    def __str__(self):
        return self.title

class Contact(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=15)
    message = models.TextField()

    def __str__(self):
        return self.message


class Qrcode(models.Model):
    name = models.CharField(max_length=255)
    link = models.URLField()
    code = models.ImageField(blank=True, upload_to='Qrcode')  # Add this line

    def save(self, *args, **kwargs):
        try:
            # Check if the instance is being added or if the 'link' field has changed
            if self._state.adding or self.link != self._original_state.get('link'):
                # Generate QR code with a dynamic size based on the length of the link
                qr_image = qrcode.make(self.link, box_size=10)

                # Calculate the canvas size dynamically based on the QR code size
                canvas_size = (qr_image.size[0] + 20, qr_image.size[1] + 20)
                qr_offset = Image.new('RGB', canvas_size, 'white')
                qr_offset.paste(qr_image, (10, 10))

                # Sanitize the filename
                base_name = os.path.basename(self.link)
                file_name = f'{base_name}-{self.id}qr.png'

                # Update the existing QR code image in the 'code' field
                if self.code:
                    # If the model already has a QR code image, delete the old file
                    self.code.delete()

                # Save the updated QR code image to the 'code' field
                stream = BytesIO()
                qr_offset.save(stream, 'PNG')
                self.code.save(file_name, File(stream), save=False)
                qr_offset.close()

            super().save(*args, **kwargs)
        except Exception as e:
            print(f"Error generating or saving QR code: {e}")
            super().save(*args, **kwargs)