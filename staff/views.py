from django.contrib import messages
from django.shortcuts import render, redirect
from django.urls import reverse

from staff.models import Product, Profile, Service, Gallery, Video, Contact


def profile(request, slug):
    product = Product.objects.order_by('order')
    profile = Profile.objects.get(slug=slug)
    service =Service.objects.all()
    gallery =Gallery.objects.all()
    videos = Video.objects.all()
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        phone = request.POST.get('phone')  # Note: I changed 'mobile' to match the HTML input name
        message = request.POST.get('message')

        # Save the form data to the Contact model
        contact = Contact(name=name, email=email, phone=phone, message=message)
        contact.save()

        # Optionally, you can add a success message
        messages.success(request, 'Your message was successfully submitted!')

        # Redirect to the same page or another page
        return redirect(reverse('profile', kwargs={'slug':slug}))

    context = {
        'product':product,
        'profile':profile,
        'service':service,
        'gallery':gallery,
        'videos':videos,
    }
    return render(request, 'profile.html', context)


