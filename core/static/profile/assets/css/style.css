html
{
	scroll-behavior: smooth;
	overflow-x: hidden;
	font-family : Futura;
}
body
{
    font-size: 16px;
    color: #000;
    background-color: #fff;
	box-sizing: border-box;
	overflow-x: hidden;
	  font-family: 'Futura', sans-serif;
}

h2{
font-family: '<PERSON><PERSON>', sans-serif;
}

.user1
{
    padding: 5px;
	/*border: 1px solid #ddd;
	box-shadow: 1px 1px 10px #ddd;*/
	max-height: 150px;
	object-fit: contain;
	margin-left: auto;
	margin-right: auto;
	display: block;
}

.content-div
{
	width: 100%;
	overflow-x: hidden;
	margin-left: auto;
	margin-right: auto;
}
.vertical-align-middle
{
    display: flex;
    align-items: center;
}
.div-center-align
{
	margin-left: auto;
    margin-right: auto;
    display: block;
}
/* === Gallery === */
.img-responsive
{
	width: 100%;
}
#lightgallery
{
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}
#lightgallery li
{
	padding: 3px;
}

.theme1-row-bg
{
    background-color: red;
}
.ratings-ul 
{
	list-style-type: none;
	text-align: center;
	margin-bottom: 10px;
}
.ratings-ul li
{
	display: inline-block;
	color: yellow;
	font-size: 22px;
}
/* Expiry Image */
.expired-div
{
	width: 100%;
	background-color: rgba(0, 0, 0, 0.5);
    /* position: absolute; */
    top: 0;
    right: 0;
    bottom: -50px;
    left: 0;
    z-index: 9999;
	position: fixed;
	overflow-y: hidden!important;
}
.expired-div img
{
	width: 40%;
	position: fixed!important;
	left: 0;
	right: 0;
	margin-top: 100px;
	margin-left: auto;
	margin-right: auto;
}
.expired-content-div
{
	background-color: #fff;
	text-align: center;
	width: 500px;
	height: auto;
	margin-left: auto;
	margin-right: auto;
	top: 25%;
	position: relative;
	padding: 20px;
	overflow-y: hidden;
	box-shadow: 1px 1px 10px #ddd;
	border: 4px double gray;
}
.expired-content-div i
{
	font-size: 48px;
	color: red;
	margin-bottom: 20px;
}
.expired-content-div h1
{
	font-weight: 700;
	font-size: 26px;
	text-align: center;
	text-transform: uppercase;
	border-bottom: 3px double gray;
	padding-bottom: 10px;
	color: #0F0F34;
}
.expired-content-div h2 a
{
	font-size: 16px;
	background-color: red;
	padding: 8px 20px;
	text-decoration: none;
	color: #fff;
	transition: all 0.3s ease;
	box-shadow: 1px 1px 10px #ddd;
}
.expired-content-div h2 a:hover
{
	background-color: gray;
}
.expired-content-div p
{
	font-weight: 500;
	text-align: center;
	font-size: 16px;
	/* color: grey; */
	line-height: 22px;
	margin-bottom: 30px;
}
.success-p
{
    border: 2px solid green;
    color: green;
    font-weight: 600;
    text-align: center;
    padding: 5px 0px;
    margin-top: 10px;
}
.success-p i
{
    margin-right: 10px;
}
/* Form Section */
label
{
    font-weight: 500;
    color: #101036;
}
.form-control
{
    color: #000;
}
.form-submit
{
    background-color: #0085C4;
    border-radius: 0px;
    padding: 7px 15px;
    font-weight: 600;
    border: 2px solid transparent;
	transition: all 0.3s ease;
	font-size: 15px;
}
.form-submit i
{
    margin-right: 5px;
}
.form-submit:hover
{
    background-color: #fff;
    color: #101036;
    box-shadow: 1px 1px 10px #ddd;
    border: 2px solid #ddd;
}
.form-prefix
{
    /* border: 2px solid red; */
    min-width: 45px!important;
    max-width: 45px!important;
    text-align: center;
}
.form-prefix i
{
    font-size: 16px;
}

/* Star Ratings */
div.stars {
    width: auto;
    display: inline-block;
	margin-top: 0px;
  }
  
  input.star { display: none; }
  
  label.star {
    float: right;
    padding: 0px 10px;
    font-size: 24px;
    color: #444;
	transition: all .2s;
	text-align: left;
  }
  
  input.star:checked ~ label.star:before {
    content: '\f005';
    color: #FFB900;
    transition: all .25s;
  }
  
  input.star-5:checked ~ label.star:before {
    color: #FFB900;
    text-shadow: 0 0 20px #952;
  }
  
  input.star-1:checked ~ label.star:before { color: #F62; }
  
  label.star:hover { transform: rotate(-15deg) scale(1.3); }
  
  label.star:before {
    content: '\f006';
    font-family: FontAwesome;
  }
/* End | Star Ratings */
.alert-error 
{
    color: #ed0202;
    padding: 0px;
    font-weight: 700;
    font-size: 14px;
    display: inline-block;
    border: none;
    margin-bottom: 0px;
	padding: 2px 8px;
	background-color: #fff;
	border-radius: 0px;
	margin-bottom: 10px;
}
/* ==== Mobile CSS ==== */
@media only screen and (max-width: 600px)
{
	.expired-div img
	{
		width: 100%;
		margin-top: 50%;
	}
	.expired-div
	{
		padding: 0px 15px;
	}
	.expired-content-div
	{
		background-color: #fff;
		text-align: center;
		width: 100%;
		height: auto;
		margin-left: auto;
		margin-right: auto;
		top: 25%;
		position: relative;
	}
	.expired-content-div h1
	{
		font-size: 22px;
	}
}
