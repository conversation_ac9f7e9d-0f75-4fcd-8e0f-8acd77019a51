:root 
{
  --primary-color: #0D2A6E;
  --secondary-color: red;
  --thrird-color: green;
}
{
 font-family: '<PERSON>o', sans-serif;
}
.main-row-div
{
    /* background-color: blue; */
	padding: 10px 0px 20px 0px;
	background: inear-gradient(90deg, rgb(255 255 255) 0%, rgb(255 255 255) 80%);
}
.home-div
{
    background-color: var(--primary-color);
	/* padding: 20px 15px; */
	border: 2px solid #00000099;
}
.home-div .logo
{
   
    max-width: 80%;
    height: auto;
    padding: 5px;
	
	max-height: 150px;
	object-fit: contain;
	margin-left: auto;
	margin-right: auto;
	display: block;
}
.home-div h1
{
    color: var(--primary-color);
	text-align: center;
	font-size: 24px;
	font-weight: 700;
	text-transform: uppercase;
	margin: 5px 0px 0px 0px;
	font-family: '<PERSON><PERSON> Neue', sans-serif;

}
.home-div h2
{
	text-align: center;
	font-size: 18px;
	/* color: #fff; */
	/* margin-bottom: 20px; */
}
.home-cta-btns-ul
{
	list-style-type: none;
	text-align: center;
	padding-left: 0px;
}
.home-cta-btns-ul li
{
	display: inline-block;
	margin: 0px 5px;
	text-align: center;
}
.home-cta-btns-ul li a
{
	text-decoration: none;
	color: #000;
	font-weight: 500;
	font-size: 14px;
	font-family: system-ui;
}
.home-cta-btns-ul li a i
{
	color: #fff;
/* 	background: #EAC33D; */
	background: #0D2A6E;
	/* padding: 7px 10px; */
	font-weight: 500;
	/* box-shadow: 0px 0px 10px 1px rgba(255,255,255,0.3); */
	text-decoration: none;
	font-size: 18px;
	width: 45px;
	height: 45px;
	line-height: 45px;
	border-radius: 50%;
	text-align: center;
	display: block;
	margin-left: auto;
	margin-right: auto;
	transition: all 0.3s ease;
}
.home-cta-btns-ul li a i:hover
{
	background-color: #fff;
	color: var(--primary-color);
	box-shadow: 1px 1px 10px #b0b0b0;
}
.contact-points-ul 
{
	list-style-type: none;
	margin-left: 0px;
	padding-left: 0px;
}
.contact-points-ul li
{
	color: #fff;
	margin: 15px 0px;
	font-weight: 500;
	font-size: 16.3px;
}
.contact-points-ul li a
{
	color: #fff;
	text-decoration: none;
	font-weight: 500;
}
.contact-points-ul li a i, .contact-points-ul li i
{
	font-size: 16px;
	background: #fff;
	color: var(--primary-color);
	width: 30px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	border-radius: 50%;
	/* box-shadow: 1px 1px 1px #fff; */
}
.contact-points-ul li a span, .contact-points-ul li span
{
	margin-left: 10px;
	font-family: system-ui;
}
.social-ul
{
	list-style-type: none;
	margin-left: 0px;
	text-align: center;
	margin-bottom: 0px;
	padding-left: 0px;
}
.social-ul li
{
	display: inline-block;
	margin: 0px 1px;
}
.social-ul li a
{
	color: #fff;
}
.social-ul li a i
{
	background: grey;
    width: 32px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    border-radius: 50%;
	transition: all 0.3s ease;
	border: 1px solid #ddd;
	font-size: 14px;
}
.social-ul li a .active-i:hover
{
	background: #fff;
	color: #000;
}
.social-ul li a .fa-facebook
{
	background-color: #3b5998;
}
.social-ul li a .fa-twitter
{
	background-color: #1da1f2;
}
.social-ul li a .fa-instagram
{
	background-color: #c32aa3;
}
.social-ul li a .fa-youtube-play
{
	background-color: #ff0000;
}
.social-ul li a .fa-linkedin
{
	background-color: #0e76a8;
}	
.section
{
	background-color: #fff;
	margin: 15px 0px;
    padding: 30px 20px;
	box-shadow: inset 0px 0px 10px rgba(0,0,0,0.2);
	padding-bottom: 10px;
}

.about-div p
{
	line-height: 22px;
    font-family: system-ui;
    font-weight: 500;
}
}
.about-div ul, .about-div ol
{
	padding-left: 30px;
}
.single-prod-serv-div
{
	background: rgb(237 244 255 / 20%);
	padding: 20px 15px 10px 15px;
	margin-bottom: 12px;
	text-align: center;
	box-shadow: 1px 1px 10px #b0b0b0;
	border: 2px solid #fff;
}

.single-prod-serv-div h3
{
	font-size: 18px;
  	margin: 0px;
	margin-bottom: 10px;
	font-weight: 600;
	/* text-align: left; */
    text-transform: uppercase;
    font-family: math;
}
.single-prod-serv-div img
{
	background: #fff;
  	width: 100%;
  	height: 100%;
    object-fit: cover;
	border: 1px solid #ddd;
	box-shadow: 1px 1px 10px #ddd;
}
.youtube-video
{
	width: 100%;
	height: 250px;
	margin-bottom: 10px;
}
.client-div-part
{
	padding: 0px 5px;
}
.client-div-part p
{
	font-weight: 500;
	font-size: 14px;
	text-align: center;
}
.client-img-bg
{
	width: 100%;
	height: 100px;
	background-color: #fff;
	padding: 5px;
	margin-bottom: 3px;
	box-shadow: 1px 1px 10px #ddd;
	border: 1px solid #ddd;
}
.client-img-bg img
{
	width: 100%;
	height: 100%;
	object-fit: contain;
}
.payment-table
{
	box-shadow: 1px 1px 10px #b0b0b0;
	border: 2px solid #fff;
}
.table-striped tbody tr:nth-of-type(odd)
{
	background-color: rgba(255,0,87, 0.2);
}
/* .payment-table tbody tr td
{
	font-weight: 500;
} */
.payment-table tbody tr td:nth-child(3)
{
	font-weight: 500;
	text-transform: uppercase;
}
.payment-table tbody tr td:nth-child(2)
{
	font-weight: 700;
	/* color: #555555; */
	/* border-right: 1px solid #ddd; */
}
.payment-table tbody tr th
{
	color: #101036;
	/* border-right: 1px solid #ddd; */
}
.review-name
{
	margin-bottom: 5px;
	font-weight: 600;
	color: #101036;
}
.review-name i
{
	margin-right: 5px;
	color: gray;
	font-size: 18px;
}
.review-star
{
	color: #FFB900;
}
.gmap
{
	width: 100%;
	height: 400px;
	box-shadow: 1px 1px 10px gray;
	border-radius: 15px;
}
.section h2
{
	text-align: center;
    text-transform: uppercase;
    font-size: 22px;
	font-weight: 700;
	color: var(--primary-color);
	margin-bottom: 8px;
}
.section h2 i
{
	margin-right: 12px;
}
.section h2:after {
	content: '';
	display: block;
	margin: auto;
	height: 7px;
	animation: underline 2s infinite;
	margin-top: 5px;
}
.home-div h1:after {
	content: '';
	display: block;
	margin: auto;
	height: 7px;
	animation: underline 2s infinite;
	margin-top: 5px;

}
@keyframes underline {
  0% { width: 0%; background-color: var(--secondary-color);}
  60% {width: 60%; background-color: transparent;}
}
.form-prefix
{
	background-color: var(--primary-color);
	color: #fff;
	border-radius: 0px!important;
	border: none;
}
.form-submit
{
    background-color: var(--primary-color);
}
.review-form
{
	background-color: rgba(255,0,87, 0.2);
	padding: 15px;
	color: #fff;
	border: 1px solid #fff;
	box-shadow: 1px 1px 10px #ddd;
}
.single-branch
{
	border: 1px solid #ddd;
	box-shadow: 1px 1px 10px #b0b0b0;
	padding: 20px;
	margin-bottom: 10px;
}
.contact-left-points-div
{
    display: flex;
    align-items: center;
}
.contact-left-points-div .icon
{
    display: inline-block;
    font-size: 22px;
    background-color: var(--primary-color);
    min-width: 50px;
    min-height: 50px;
    /* border-radius: 50%; */
    text-align: center;
    color: #fff;
    line-height: 50px;
    margin-right: 20px;
}
.contact-left-points-div .text
{
    display: inline-block;
    font-size: 16px;
    /* font-weight: 600; */
	color: #000;
}
.contact-left-points-div .text .heading
{
    /* color: var(--primary-color); */
	font-size: 18px;
	font-weight: 600;
	text-transform: uppercase;
}
.contact-left-points-div .text .address
{
	font-size: 16px;
	text-align: center;
}

.single-video h3
{
	text-align: center;
	font-size: 18px;
}
.img-responsive
{
    ox-shadow: 1px 1px 10px #fff;
    height: 100px;
    object-fit: cover;
    width: 119px;
    padding-right: 3px;
    padding-bottom: 3px;
}
.single-pdf
{
	border: 1px solid #ddd;
	box-shadow: 1px 1px 10px #b0b0b0;
	padding: 20px 10px;
	text-align: center;
	border-radius: 10px;
	margin-bottom: 15px;
}
.single-pdf .fa-file-pdf-o
{
	width: 50px;
	height: 50px;
	line-height: 50px;
	display: block;
	border-radius: 50%;
	/* box-shadow: 1px 1px 10px #b0b0b0; */
	text-align: center;
	margin-left: auto;
	margin-right: auto;
	background-color: var(--primary-color);
	color: #fff;
	font-weight: 600;
	font-size: 18px;
}
.single-pdf h3
{
	font-size: 20px;
	margin-top: 10px;
	/* border-bottom: 1px solid gray; */
}
.primary-btn
{
	border-radius: 0px;
	background-color: var(--primary-color);
	border: none;
	min-width: 100px;
	padding: 5px 10px;
	font-weight: 500;
	transition: all 0.3s ease;
}
.primary-btn i
{
	margin-right: 10px;
}
.primary-btn:hover
{
	background-color: #fff;
	color: var(--primary-color);
	box-shadow: 1px 1px 10px #b0b0b0;
}
.about-gst
{
	background: rgba(255,0,87, 0.2);
	padding: 10px 20px;
	/* box-shadow: 1px 1px 10px #ddd; */
	font-weight: 500;
}
.about-content
{
	box-shadow: 1px 1px 10px #ddd;
	padding: 10px;
}
.navigation-div
{
	position: fixed;
	bottom: 0px;
  	background: #fff;
  	width: 100%;
	text-align: center;
	min-height: 50px;
	z-index: 999;
}
.navigation-div ul
{
	list-style-type: none;
	padding-top: 8px;
	overflow: auto;
	margin-bottom: 0px;
	padding-left: 0px;
	display: block;
	/* text-align: center; */
	/* justify-content: center; */
	/*margin-left: auto!important;
	margin-right: auto!important;
	text-align: center;*/
}
.navigation-div ul li
{
	display: inline-block;
	text-align: center;
	padding: 0px 5px;
	min-width: 80px;
}
.navigation-div ul li i
{
	display: block;
	font-size: 16px;
	color: var(--primary-color);
	width: 30px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	background-color: #fff;
	margin-left: auto;
	border-radius: 50%;
	/* box-shadow: 1px 1px 10px #ddd; */
	border: 1px solid var(--primary-color);
	margin-right: auto;
	transition: all 0.3s ease;
}
.navigation-div ul li a
{
	color: #000;
	text-decoration: none;
	font-weight: 600;
	font-size: 15px;
}
.copyright-div
{
	border: 0px;
	padding-top: 0px;
	padding-bottom: 10px;
	margin-top: -10px;
}
.copyright-div p
{
	text-align: center;
	padding-top: 10px;
	font-size: 15px;
	font-weight: 600;
}
.copyright-div p i
{
	color: red;
}
.copyright-div p a
{
	color: #000;
	text-decoration: underline;
}
.copyright-div p a:hover
{
	color: blue;
}
.shop-now-btn
{
	display: inline-block;
	margin-left: auto;
	margin-right: auto;
	text-align: center;
	font-size: 17px;
}
.shop-now-btn a
{
	background-color: #fff;
	color: #fff;
	padding: 8px 30px;
	text-decoration: none;
	border: 1px solid #fff;
	background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
	background-size: 400% 400%;
	animation: gradient 1.5s ease infinite;
	transition: all 0.3s ease;
}
@keyframes gradient {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}
.shop-now-btn a:hover
{
	background: #fff;
	color: var(--primary-color);
	box-shadow: 1px 1px 10px #b0b0b0;
}
.shop-now-btn a i
{
	margin-right: 10px;
}
.shop-now-btn-div
{
	text-align: center;
	margin: 20px 0px;
}
.whatsapps-share-div
{
	text-align: center;
	margin-left: auto;
	margin-right: auto;
	display: block;
}
.whatsapps-share-div form
{
	display: flex;
	justify-content: center;
}
.whatsapps-share-div form .input-group-prepend div
{
	border-radius: 0px;
	font-weight: 500;
}
.whatsapps-share-div form input
{
	border-radius: 0px;
	border: none;
}
.whatsapps-share-div form input::placeholder
{
	font-size: 14px;
}
.whatsapp-share-btn
{
	background-color: #25D366;
	border: 2px solid transparent;
	border-radius: 0px;
	font-size: 14px;
	font-weight: 500;
	color: #fff;
}
.whatsapp-share-btn:hover
{
	background-color: #fff;
	color: var(--primary-color);
	border: 2px solid #fff;
	/* box-shadow: 1px 1px 10px #ddd; */
}
.whatsapp-share-btn i
{
	margin-right: 5px;
}
.theme-share-option
{
	display: inline-block;
}
.home-2-btns
{
	text-align: center;
	margin: 10px 0px 20px 0px;
}
.home-2-btns .btn1, .home-2-btns .theme-share-option .btn-primary
{
	background-color: rgb(214 32 99);
	border-radius: 0px;
	color: #fff;
	border-color: transparent;
	min-width: 180px;
	font-weight: 500;
	margin: 0px 5px;
	border-bottom: 5px solid rgba(0,0,0,0.5);
	transition: all 0.3s ease;
	font-family: 'Bebas Neue', sans-serif;
}
.home-2-btns .btn1:hover, .home-2-btns .theme-share-option .btn-primary:hover
{
	background-color: rgba(0,0,0,0.5);
}
.home-2-btns .btn1 i, .home-2-btns .theme-share-option .btn-primary i
{
	padding-right: 10px;
}
.dropdown-menu a i
{
	padding-right: 5px;
}
.home-div-white-div
{
	background-color: #fff;
	padding: 10px 10px 1px 10px;
	/* box-shadow: inset 1px 1px 10px #ddd; */
	box-shadow: inset 0px 0px 10px rgba(0,0,0,0.5);
}
.home-div-content-div
{
	padding: 0px 10px 20px 10px;
}

.ratings-ul 
{
	list-style-type: none;
	text-align: center;
	margin-bottom: 8px;
	padding-left: 0px;
}
.ratings-ul li
{
	display: inline-block;
	color: #FFB900;
	font-size: 22px;
}
/* Product Adv Section */
#module {
	width: 100%;
	font-size: 15px;
	line-height: 1.5;
	margin-top: 10px;
  }
  


  #module .read-more-a
  {
	  background-color: var(--primary-color);
	  color: #fff;
	  /* padding: 7px 25px; */
	  text-decoration: none;
	  font-weight: 500;
	  height: 40px;
	  width: 40px;
	  line-height: 40px;
	  display: inline-block;
	  border-radius: 50%;
	  font-size: 16px;
  }
.prod-show-more-div
{
	margin-bottom: 20px;
}
.prod-show-more-div p
{
	/* text-align: justify; */
	font-weight: 500;
	font-size: 15.5px;
	/* color: gray; */
}
.prod-show-more-div h4
{
	text-align: left;
	font-weight: 700;
	color: #101036;
	padding-left: 10px;
	font-size: 18px;
}
/* .prod-show-more-div h4 i
{
	padding-right: 5px;
} */
.prod-show-more-div h4 .disc
{
	float: right;
	font-size: 16px;
}
.prod-show-more-div h4 .disc span
{
	width: 40px;
	height: 40px;
	line-height: 40px;
	background-color: var(--primary-color);
	border-radius: 50%;
	font-size: 12px;
	display: inline-block;
	color: #fff;
	font-weight: 700;
	text-align: center;
}
.prod-show-more-div h4 .dashed-price
{
	font-size: 16px;
	margin-left: 20px;
}
.prod-btns
{
	margin-top: 10px;
	clear: both;
}
.prod-ind-btn
{
	border-radius: 0px;
    font-size: 14px;
    font-weight: 600;
    background: var(--primary-color);
	border: none;
	margin: 0px 10px;
	min-width: 35%;
	transition: all 0.3s ease;
	border: 1px solid transparent;
}
.prod-ind-btn:hover
{
	background-color: #fff;
	box-shadow: 1px 1px 10px #ddd;
	color: #101036;
	border: 1px solid #ddd;
}
.prod-ind-btn i
{
	padding-right: 10px;
}
.modal-title
{
	margin-left: auto;
	text-transform: uppercase;
	color: #101036;
	font-weight: 700;
}
.modal-close-btn
{
	border-radius: 0px;
}
.modal-close-btn i
{
	margin-right: 5px;
}
.navigation-div ul li a:hover i
{
	background-color: var(--primary-color);
	box-shadow: 1px 1px 10px #aaa;
	color: #fff;
}
.whatsapp-enquiry
{
	background-color: #44C455;
}
/* ==== Mobile CSS ==== */
@media only screen and (max-width: 600px)
{
    .main-row-div
	{
		padding: 10px 10px 20px 10px;
	}
	.navigation-div ul
	{
		display: flex;
	}
	.img-responsive
	{
		height: 100px;
	}
	.whatsapps-share-div form .input-group
 	{
		width: 50%!important;
	}
	.whatsapps-share-div
	{
		margin-left: -10px;
		margin-right: -10px;
		text-align: center;
	}
	.whatsapps-share-div form input::placeholder
	{
		font-size: 12px;
	}
	.contact-points-ul li
	{
		font-size: 15.5px;
	}
	.home-2-btns .btn1, .home-2-btns .theme-share-option .btn-primary
	{
		min-width: 140px;
		margin: 0px;
		font-size: 14px;
	}
	.section
	{
		padding: 30px 10px;
	}
	.clients-section 
	{
		padding: 30px 20px;
	}
	.copyright-div
	{
		border: 0px;
		padding-top: 0px;
		padding-bottom: 10px;
		margin-top: -10px;
	}
	.single-prod-serv-div
	{
		padding: 20px 10px 10px 10px;
	}
	#module .read-more-a
	{
		font-size: 18px;
	}
	.prod-ind-btn
	{
		min-width: auto;
		margin: 0px 3px;
	}
	.shop-now-btn a
	{
		padding: 8px 40px;
	}
	.home-div .logo
	{
		min-height: 80px;
	}
}
