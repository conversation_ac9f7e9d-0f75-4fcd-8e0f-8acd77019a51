from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from .models import ServiceCategory, Blog, Career, Services


class OtherSitemap(Sitemap):
    priority = 1.0

    def items(self):
        return ['home', 'about', 'services_category', 'products', 'portfolio', 'partners', 'blogs', 'pr_release', 'career', 'contact']

    def location(self, item):
        return reverse(item)

class ServiceCategorySitemap(Sitemap):
    priority =  1.0
    def items(self):
        return list(ServiceCategory.objects.all()) + list(Services.objects.all())

    def lastmod(self, obj):
        return obj.updated_date

    def location(self, obj):
        # Determine the URL based on the model type
        if isinstance(obj, ServiceCategory):
            return reverse('services', args=[obj.slug])
        elif isinstance(obj, Services):
            return reverse('services_details', args=[obj.slug])
        else:
            # Handle other models if needed
            return ''

class BlogSitemap(Sitemap):
    priority =  1.0
    def items(self):
        return Blog.objects.filter(type='Blog')

    def lastmod(self, obj):
        return obj.updated_date

    def location(self, obj):
        return reverse('blog_details', args=[obj.slug])

class PrSitemap(Sitemap):
    priority =  1.0
    def items(self):
        return Blog.objects.filter(type='PR RELEASE')

    def lastmod(self, obj):
        return obj.updated_date

    def location(self, obj):
        return reverse('pr_release_details', args=[obj.slug])

class CareerSitemap(Sitemap):
    priority =  1.0
    def items(self):
        return Career.objects.all()

    def lastmod(self, obj):
        return obj.updated_date

    def location(self, obj):
        return reverse('career_details', args=[obj.slug])


sitemaps = {
    'other_sitemap': OtherSitemap,
    'service_sitemap': ServiceCategorySitemap,
    'blog_sitemap': BlogSitemap,
    'pr_sitemap': PrSitemap,
    'career_sitemap': CareerSitemap,
}