from django.urls import path
from core import views


urlpatterns = [
    path('', views.home, name='home'),
    path('about/', views.about, name='about'),

    path('services/', views.services_category, name='services_category'),
    path('services/<slug:slug>/', views.services, name='services'),
    path('services-details/<slug:slug>/', views.services_details, name='services_details'),

    path('products/', views.products, name='products'),
    path('portfolio/', views.portfolio, name='portfolio'),
    path('partners/', views.partners, name='partners'),
    path('blogs/', views.blogs, name='blogs'),
    path('blog/<slug:slug>/', views.blog_details, name='blog_details'),
    path('pr/', views.pr_release, name='pr_release'),
    path('pr/<slug:slug>/', views.pr_release_details, name='pr_release_details'),

    path('career/', views.career, name='career'),
    path('career/<slug:slug>/', views.career_details, name='career_details'),

    path('magazines/', views.magazines, name='magazines'),
    path('magazines/<slug:slug>/', views.magazines_details, name='magazines_details'),

    path('contact/', views.contact, name='contact'),
    path('newsletter/', views.newsletter, name='newsletter'),

    path('under-construction/', views.under_construction, name='under_construction'),

]