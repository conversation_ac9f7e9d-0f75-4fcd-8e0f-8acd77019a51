# Generated by Django 4.2.1 on 2023-09-04 05:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0038_alter_portfolio_area'),
    ]

    operations = [
        migrations.AddField(
            model_name='blog',
            name='alt_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='blog',
            name='seo_des',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='blog',
            name='seo_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='career',
            name='seo_des',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='career',
            name='seo_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='partner',
            name='alt_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='portfolio',
            name='alt_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='seo_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecategory',
            name='alt_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecategory',
            name='seo_des',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecategory',
            name='seo_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='services',
            name='alt_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='alt_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='area',
            field=models.CharField(choices=[('ROW ONE', 'ROW ONE'), ('HOME', 'HOME'), ('ROW TWO', 'ROW TWO')], max_length=255),
        ),
    ]
