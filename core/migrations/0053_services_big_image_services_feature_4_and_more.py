# Generated by Django 4.2.7 on 2024-01-15 06:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0052_rename_seo_title_product_alt_title_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='services',
            name='big_image',
            field=models.FileField(default=3, upload_to='Services'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='services',
            name='feature_4',
            field=models.CharField(default=1, max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='services',
            name='seo_des',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='services',
            name='seo_title',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='services',
            name='short_des',
            field=models.CharField(default=6, max_length=300),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='services',
            name='slug',
            field=models.SlugField(blank=True, max_length=200, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='services',
            name='small_image',
            field=models.FileField(default=4, upload_to='Services'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='area',
            field=models.CharField(choices=[('ROW TWO', 'ROW TWO'), ('ROW ONE', 'ROW ONE'), ('HOME', 'HOME')], max_length=255),
        ),
    ]
