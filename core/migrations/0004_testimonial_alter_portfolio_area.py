# Generated by Django 4.2.1 on 2023-05-11 07:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_alter_blog_description_alter_portfolio_area'),
    ]

    operations = [
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purpose', models.Char<PERSON>ield(max_length=255)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('company', models.CharField(max_length=255)),
                ('position', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('review', models.<PERSON><PERSON><PERSON><PERSON>(max_length=500)),
                ('image', models.ImageField(upload_to='Testimonial')),
            ],
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='area',
            field=models.CharField(choices=[('ROW TWO', 'ROW TWO'), ('ROW ONE', 'ROW ONE'), ('HOME', 'HOME')], max_length=255),
        ),
    ]
