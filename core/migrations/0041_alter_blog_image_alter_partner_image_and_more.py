# Generated by Django 4.2.1 on 2023-09-04 07:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0040_blog_updated_date_career_updated_date_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='blog',
            name='image',
            field=models.FileField(upload_to='Blogs'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='partner',
            name='image',
            field=models.FileField(upload_to='Partner'),
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='area',
            field=models.CharField(choices=[('ROW TWO', 'ROW TWO'), ('ROW ONE', 'ROW ONE'), ('HOME', 'HOME')], max_length=255),
        ),
        migrations.AlterField(
            model_name='servicecategory',
            name='image',
            field=models.FileField(upload_to='ServiceCategory'),
        ),
        migrations.AlterField(
            model_name='services',
            name='image',
            field=models.FileField(upload_to='Services'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='testimonial',
            name='image',
            field=models.File<PERSON>ield(upload_to='Testimonial'),
        ),
    ]
