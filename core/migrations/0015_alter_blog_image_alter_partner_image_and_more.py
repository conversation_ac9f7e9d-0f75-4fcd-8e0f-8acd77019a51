# Generated by Django 4.2.1 on 2023-07-24 04:42

from django.db import migrations
import django_resized.forms


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0014_remove_applyjobs_current_salary_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='blog',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='Blogs'),
        ),
        migrations.AlterField(
            model_name='partner',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='Partner'),
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='Portfolio'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='Product'),
        ),
        migrations.AlterField(
            model_name='servicecategory',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='ServiceCategory'),
        ),
        migrations.AlterField(
            model_name='services',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='Services'),
        ),
        migrations.AlterField(
            model_name='testimonial',
            name='image',
            field=django_resized.forms.ResizedImageField(crop=None, force_format=None, keep_meta=True, quality=100, scale=None, size=[500, 500], upload_to='Testimonial'),
        ),
    ]
