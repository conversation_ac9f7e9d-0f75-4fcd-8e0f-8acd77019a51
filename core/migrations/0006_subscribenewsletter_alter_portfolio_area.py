# Generated by Django 4.2.1 on 2023-05-11 09:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_alter_portfolio_area'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscribeNewsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('created', models.DateField(auto_now=True)),
            ],
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='area',
            field=models.CharField(choices=[('HOME', 'HOME'), ('ROW ONE', 'ROW ONE'), ('ROW TWO', 'ROW TWO')], max_length=255),
        ),
    ]
