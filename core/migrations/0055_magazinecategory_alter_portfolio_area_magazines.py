# Generated by Django 4.2.7 on 2024-01-24 07:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0054_services_updated_date_alter_portfolio_area'),
    ]

    operations = [
        migrations.CreateModel(
            name='MagazineCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('category', models.CharField(choices=[('FMCG_HORECA_MAGAZINES', 'FMCG HORECA MAGAZINES'), ('WONDERMOM_MAGAZINES', 'WONDERMOM MAGAZINES')], max_length=255)),
                ('subtitle', models.CharField(max_length=255)),
                ('image', models.FileField(upload_to='Magazines')),
                ('link', models.URL<PERSON>ield(max_length=255)),
                ('description', models.TextField()),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('updated_date', models.DateField(auto_now=True, null=True)),
                ('seo_title', models.TextField(blank=True, null=True)),
                ('alt_title', models.TextField(blank=True, null=True)),
                ('seo_description', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.AlterField(
            model_name='portfolio',
            name='area',
            field=models.CharField(choices=[('ROW TWO', 'ROW TWO'), ('ROW ONE', 'ROW ONE'), ('HOME', 'HOME')], max_length=255),
        ),
        migrations.CreateModel(
            name='Magazines',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.CharField(max_length=255)),
                ('name', models.CharField(max_length=255)),
                ('image', models.FileField(upload_to='Magazines')),
                ('link', models.URLField()),
                ('magazine_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.magazinecategory')),
            ],
        ),
    ]
