from django import forms

from core.models import ApplyJobs, Contact, SubscribeNewsletter


class ApplyJobForm(forms.ModelForm):
    class Meta:
        model = ApplyJobs
        fields = ('name', 'email', 'phone', 'cv')


class ContactForm(forms.ModelForm):
    class Meta:
        model = Contact
        fields = ('name', 'email', 'phone', 'subject', 'message')



class NewsletterForm(forms.ModelForm):
    class Meta:
        model = SubscribeNewsletter
        fields = ('email', )

