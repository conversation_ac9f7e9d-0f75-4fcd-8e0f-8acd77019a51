
 <div class="footer__item-6" id="newsletter" role="group">
                    <h2 class="footer__item-title">newsletter</h2>
                    <form>
                            {% csrf_token %}

                      <div class="footer__newsletter-6">
                        <input  type="email" name="email" placeholder="Enter Your Email">
                        <button hx-post="{% url 'newsletter' %}" hx-target="#newsletter" hx-swap="outerHTML" aria-label="Subscribe to Newsletter"><i class="fa-solid fa-arrow-right"></i></button>
                      </div>
                      <div class="footer__chekbox">
                       <input type="checkbox" id="check_box" name="checkbox" aria-label="Subscribe to Newsletter">
                        <label>I’m okay with getting emails and having that activity and privacy policy.</label>
                      </div>
                          <div class="footer__chekbox">
                      {%for message in messages %}
     {%if "newsletter" in message.tags %}
                        <label>{{message}}  {# show login error message #}</label>
                          {%endif%}
{%endfor%}

                      </div>
                    </form>
                  </div>


 <script src="https://unpkg.com/htmx.org@1.6.0"></script>
    <script>
        document.body.addEventListener('htmx:configRequest', (e) => {
          e.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
          })
      </script>

