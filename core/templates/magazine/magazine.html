{% extends 'include/base.html' %}


{% block head %}

<title>Magazines | Gulf Time Media - Your Premier Source for Business Solutions</title>
<meta name="description" content="Discover the latest trends and insights in the FMCG & HORECA industry with Gulf Time Media's E-Magazines. Your go-to platform for premium media services and expert consultancy in the UAE. Contact Us today.">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="E-Magazines | Gulf Time Media - Your Premier Source for Business Solutions">
<meta property="og:description" content="Explore the latest trends and insights in the FMCG & HORECA industry with Gulf Time Media's E-Magazines. Providing premium media services and expert consultancy for your business. Contact Us today.">
<meta property="og:image" content="https://gulftimemedia.ae/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="E-Magazines | Gulf Time Media - Your Premier Source for Business Solutions">
<meta name="twitter:description" content="Explore the latest trends and insights in the FMCG & HORECA industry with Gulf Time Media's E-Magazines. Your go-to platform for premium media services and expert consultancy in the UAE. Contact Us today.">
<meta name="twitter:image" content="https://gulftimemedia.ae/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}


{% block content %}
{% load static %}


<div id="smooth-content">
<main>

  <section class="service__area-6" style="margin-top: 30px;">
    <div class="container">
        <div class="row inherit-row">
            <div class="col-xxl-12">
                <div class="content-wrapper" style="margin-top: 67px;">
                    <div class="left-content">
                        <ul class="service__list-6">
                            {% for magazine in magazine %}
                            <li class="active" id="{{ magazine.id }}"><a href="#{{ magazine.id }}">{{ magazine.title }}</a></li>
                            {% endfor %}
                        </ul>
                    </div>

                    <div class="mid-content" id="magazine-content-area">
                        {% for magazine in magazine %}
                            <div class="service__image" id="{{ magazine.id }}">
                                <img src="{{ magazine.image.url }}" alt="Service Image">
                            </div>
                        {% endfor %}
                    </div>

                    <div class="right-content" style="margin-top: 50px">
                        <div class="service__items-6">
                            {% for magazine in magazine %}
                                <div class="service__item-6 has__service_animation" id="{{ magazine.id }}" data-secid="1">
                                    <div class="image-tab">
                                        <img src="{{ magazine.image.url }}" alt="Service Image">
                                    </div>
                                    <div class="animation__service_page">
                                        <h2 class="service__title-6">{{ magazine.subtitle }}</h2>
                                        <p>{{ magazine.description }}</p>
                                        <ul>
                                            <li>+ Advertisements</li>
                                            <li>+ Digital Magazines</li>
                                            <li>+ Events</li>
                                            <li>+ News</li>
                                            <li>+ Subscriptions</li>
                                            <li>+ Trending Insights</li>
                                        </ul>
                                        <div class="btn_wrapper" style="padding-bottom: 50px;">
                                            <a href="{% url 'magazines_details' magazine.slug %}" class="wc-btn-secondary btn-item btn-hover">
                                                <span></span>View<br>Magazines<br>
                                                <i class="fa-solid fa-arrow-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</section>

<script>
    function myFunction(magazineId) {
        const element = document.getElementById(magazineId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }
</script>




{% endblock %}