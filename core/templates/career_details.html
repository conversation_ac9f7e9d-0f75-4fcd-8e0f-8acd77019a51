{% extends 'include/base.html' %}

{% block head %}

<title>{{ career.seo_title }} | {{ seo_title }}</title>
<meta name="description" content="{{ career.seo_des }} | {{ seo_title }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ career.seo_title }} | {{ seo_title }}">
<meta property="og:description" content="{{ career.seo_des }} | {{ seo_title }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ career.seo_title }} | {{ seo_title }}">
<meta name="twitter:description" content="{{ career.seo_des }} | {{ seo_title }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}

{% block content %}
{% load static %}


  <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>

        <!-- Job detail start -->
        <section class="job__detail" style="padding-top: 75px; background-color: #f6f6f6;">
          <div class="container g-0 pb-110">

            <div class="row">
              <div class="col-xxl-9 col-xl-9 col-lg-8 col-md-8">
                <div class="job__detail-wrapper">
                  <h2 class="sec-title">{{ career.title }}</h2>
                  <ul class="job__detail-meta">
                    <li><span>Location</span> {{ career.location }}</li>
                    <li><span>Date</span> {{ career.date }}</li>
                    <li><span>Job Type</span> {{ career.job_type }}</li>
                  </ul>
                  <hr style="margin: 2rem 0">
                  <div>
                    {{ career.description|safe }}</div>

                  <div class="job__apply btn_wrapper">
                    <button class="wc-btn-primary btn-hover btn-item"><span></span> Apply this <br>Position <i
                        class="fa-solid fa-arrow-right"></i></button>
                  </div>


                </div>
              </div>
              <div class="col-xxl-3 col-xl-3 col-lg-4 col-md-4">
                <div class="job__detail-sidebar">
                  <ul>
                    <li><span>Experience</span> {{ career.experience }} Years Experience</li>
                    <li><span>Working Hours</span> 08:30 AM to 05:30 PM</li>
                    <li><span>Working Days</span> Weekly 6 days (Mon to Sat)</li>
                    <li><span>Salary</span> {{ career.salary }} (Monthly)</li>
                    <li><span>Vacancy</span> No of Vacancies: {{ career.role_count }}</li>
                    <li><span>Deadline</span> {{ career.deadline }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- Job detail end -->

         <!-- Modal 1 -->
  <div class="modal__application" id="application_form" style="padding-top:75px;">
    <div class="modal__apply">
      <button class="modal__close-2" id="apply_close"><i class="fa-solid fa-xmark"></i></button>
      <div class="form-top">
        <h2 class="sec-title">{{ career.title }}</h2>
        <p>{{ career.job_type }}</p>
      </div>
    <div class="form-apply">
        <form method="post" enctype="multipart/form-data">
          {% csrf_token %}

          <div class="input-apply-2">
            <p>Name *</p>
            <input type="text" name="name" required>
          </div>
          <div class="input-apply-2">
            <p>Email *</p>
            <input type="email" name="email" required>
          </div>
          <div class="input-apply-2">
            <p>Phone *</p>
            <input type="text" name="phone" required>
          </div>
          <div class="input-apply-2">
            <p>Upload CV *</p>
            <input type="file" name="cv" required>
          </div>
          <div class="form-btn-2">
        <button type="submit" class="wc-btn-primary btn-hover"><span></span> Submit <i
            class="fa-solid fa-arrow-right"></i></button>
      </div>
        </form>
      </div>


    </div>
  </div>

   {% for message in messages %}

<script>
     window.onload = function() {
     alert('{{message}}')
     }
</script>
                                {% endfor %}

          <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Products",
      "url": "{{ request.scheme }}://{{ request.get_host }}/products/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/products/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Services",
          "url": "{{ request.scheme }}://{{ request.get_host }}/services/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/services/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>


      {% endblock %}

