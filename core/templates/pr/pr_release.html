{% extends 'include/base.html' %}


{% block head %}

<title>{{ seo_title }}</title>
<meta name="description" content="{{ seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ seo_title }}">
<meta property="og:description" content="{{ seo_des }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ seo_title }}">
<meta name="twitter:description" content="{{ seo_des }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">


 <style>
    .pagination .step-links {
      display: inline-block;
      margin-bottom: 10px;
    }

    .pagination .step-links a {
      display: inline-block;
      padding: 5px 10px;
      margin: 0 5px;
      border: 1px solid #ccc;
      background-color: #f4f4f4;
      color: #333;
      text-decoration: none;
      border-radius: 4px;
    }

    .pagination .step-links a:hover {
      background-color: #e0e0e0;
    }

    .pagination .current {
      margin: 0 10px;
      display: inline-block;
    }

    /* Center the pagination buttons */
    .pagination .button-container {
      text-align: center;
    }
  </style>

{% endblock %}


{% block content %}
{% load static %}

 <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>
      <!-- Blog area start -->
      <section class="blog__area-6 blog__animation" style="background-color: #f6f6f6; margin-top: 75px;">
        <div class="container g-0">
       <center><h2 class="sec-subtile-6" style="margin-top: 53px;">PR RELEASES</h2>
                    <h3 class="sec-title-6 title-anim">OUR RELEASES</h3></center>
                <h2 class="brand__title-3 title-anim">We work with the best in the industry
                </h2>

          <div class="row reset-grid">

            {% for pr_release in page_obj %}

            <div class="col-xxl-4 col-xl-4 col-lg-4 col-md-4">
              <article class="blog__item">
                <div class="blog__img-wrapper">
                  <a href="#">
                    <div class="img-box">
                      <img class="image-box__item" src="{{ pr_release.image.url }}" alt="{{ pr_release.name }}">
                      <img class="image-box__item" src="{{ pr_release.image.url }}" alt="{{ pr_release.name }}">
                    </div>
                  </a>
                </div>
                <h4 class="blog__meta">{{ pr_release.category }} . {{ pr_release.opendate }}</h4>
                <h5><a href="{% url 'pr_release_details' pr_release.slug %}" class="blog__title">{{ pr_release.name|truncatechars:60 }}</a></h5>
                <a href="{% url 'pr_release_details' pr_release.slug %}" class="blog__btn">Read More <span><i
                      class="fa-solid fa-arrow-right"></i></span></a>
              </article>
            </div>

            {% endfor %}

          </div>
        </div>
      </section>
      <!-- Blog area end -->

              <!-- Pagination -->
<div class="flex ">

<div class="pagination  text-center justify-content-center" style="background: #f6f6f6;">

  <span class="step-links">
    {% if start_page > 1 %}
      <a href="?page=1" class="btn btn-secondary">&laquo; First</a>
    {% endif %}
    {% if page_obj.has_previous %}
      <a href="?page={{ page_obj.previous_page_number }}" class="btn btn-secondary">&laquo; Previous</a>
    {% endif %}
  </span>

  <div class="button-container">
    <span class="step-links">
      {% for num in page_obj.paginator.page_range %}
        {% if num >= start_page and num <= end_page %}
          <a href="?page={{ num }}" class="btn btn-secondary">{{ num }}</a>
        {% endif %}
      {% endfor %}
    </span>
  </div>

  <span class="step-links">
    {% if page_obj.has_next %}
      <a href="?page={{ page_obj.next_page_number }}" class="btn btn-secondary">Next &raquo;</a>
    {% endif %}
    {% if end_page < paginator.num_pages %}
      <a href="?page={{ paginator.num_pages }}" class="btn btn-secondary">Last &raquo;</a>
    {% endif %}
  </span>
</div>
</div>
<!-- End Pagination -->

            <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "About Us",
      "url": "{{ request.scheme }}://{{ request.get_host }}/about/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/about/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Services",
          "url": "{{ request.scheme }}://{{ request.get_host }}/services/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/services/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>


      {% endblock %}