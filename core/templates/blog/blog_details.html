{% extends 'include/base.html' %}

{% block head %}

<title>{{ blogs.seo_title }} | {{ seo_title }}</title>
<meta name="description" content="{{ blogs.seo_des }} | {{ seo_title }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ blogs.seo_title }} | {{ seo_title }}">
<meta property="og:description" content="{{ blogs.seo_des }} | {{ seo_title }}">
<meta property="og:image" content="{{ blogs.image.url }}">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ blogs.seo_title }} | {{ seo_title }}">
<meta name="twitter:description" content="{{ blogs.seo_des }} | {{ seo_title }}">
<meta name="twitter:image" content="{{ blogs.image.url }}">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}

{% block content %}
{% load static %}

 <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>

        <!-- Blog detail start -->
        <section class="blog__detail" style="padding-top: 75px; background-color: #f6f6f6;">
          <div class="container g-0 pt-140">
            <div class="row">
              <div class="col-xxl-8 col-xl-10 offset-xxl-2 offset-xl-1">
                <div class="blog__detail-top">
                  <h2 class="blog__detail-date animation__word_come">{{ blogs.category }}<span>{{ blogs.post_date }}</span></h2>
                  <h3 class="blog__detail-title animation__word_come">{{ blogs.name }}
                  </h3>
                  <div class="blog__detail-metalist">
                    <div class="blog__detail-meta">
                      <p>Writen by <span>Gulf Time Media LLC</span></p>
                    </div>
                    <div class="blog__detail-meta">
                      <p>Viewed <span>{{ blogs.viewed_count }} Views</span></p>
                    </div>
                  </div>
                </div>
              </div>
<!--              <div class="col-xxl-12">-->
<!--                <div class="blog__detail-thumb">-->
<!--                  <img src="{{ blogs.image.url }}" alt="{{ blogs.name }}" data-speed="0.5">-->
<!--                </div>-->
<!--              </div>-->
              <div class="col-xxl-8 col-xl-10 offset-xxl-2 offset-xl-1">
                <div class="blog__detail-content"  style="padding: 0;">
                 {{ blogs.description|safe }}
              </div>
            </div>
            </div>
          </div>
        </section>
        <!-- Blog detail end -->


<script type="application/ld+json">
    {
        "@context": "https://schema.org/",
        "@type": "BlogPosting",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "{{ request.build_absolute_uri }}"
        },
        "author": {
            "@type": "Person",
            "name": "Gulf Time Media LLC",
            "url": "{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.web"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Gulf Time Media LLC",
            "logo": {
                "@type": "ImageObject",
                "url": "{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.web"
            }
        },
        "image": {
            "@type": "ImageObject",
            "url": "{{ blogs.image.url }}",
            "height": "{{ blogs.image.height }}",
            "width": "{{ blogs.image.width }}"
        },
        "description": "{{ blogs.descriptions|striptags|safe }}",
        "interactionStatistic": {
            "@type": "InteractionCounter",
            "interactionType": "https://schema.org/ReadAction",
            "userInteractionCount": "{{ blogs.viewed_count }}"
        },
        "url": "{{ request.build_absolute_uri }}",
        "headline": "{{ blogs.seo_title }}",
        "alternativeHeadline": "{{ blogs.alt_title }}",
        "name": "{{ blogs.seo_title }}"
    }
</script>

{% for blog_url in blog_urls %}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org/",
        "@type": "WebSite",
        "name": "{{ blog_url.seo_title }}",
        "url": "{{ request.scheme }}://{{ request.get_host }}/blog/{{ blog_url.slug }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/blog/{{ blog_url.slug }}/{search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
{% endfor %}

      {% endblock %}