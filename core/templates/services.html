{% extends 'include/base.html' %}


{% block head %}

<title>{{ servicecategory.seo_title }} | {{seo_title}}</title>
<meta name="description" content="{{ servicecategory.seo_des }} | {{ seo_title }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ servicecategory.seo_title }} | {{seo_title}}">
<meta property="og:description" content="{{ servicecategory.seo_des }} | {{seo_title}}">
<meta property="og:image" content="{{ servicecategory.image.url }}">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ servicecategory.seo_title }} | {{seo_title}}">
<meta name="twitter:description" content="{{ servicecategory.seo_des }} | {{seo_title}}">
<meta name="twitter:image" content="{{ servicecategory.image.url }}">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}


{% block content %}
{% load static %}


  <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>

        <!-- Service area start -->
        <section class="service__area-3 service-v4 pb-150" style="padding-top: 75px; background-color:black;">
          <div class="container">
            <div class="row">
              <div class="col-xx-12" style="margin-top: 40px;">
                <div class="service__list-3">

                   <div class="service__item-3 service_animation extra-ser" style="grid-template-columns: 50% 50% 20%; padding-bottom: 10px;">
                    <h3 class="sec-title title-anim" style="color:white; font-size: 39px;">{{ servicecategory.title }}</h3>
<!--                    <div class="service__content-3">-->
<!--                      <p>{{ servicecategory.description }}-->
<!--                      </p>-->
<!--                    </div>-->

          <div class="cta__content-4 text-anim">
<!--            <a class="btn-started" href="">Get a Enquiry <span><i-->
<!--                  class="fa-solid fa-arrow-right"></i></span></a>-->
                <p style="color:white; padding-bottom: 0px; margin-top: 0px; ">{{ servicecategory.description }}
                      </p>
          </div>
                  </div>

                  {% for services in services %}

                  <div class="service__item-3 service_animation">
                    <h3><a href="{% url 'services_details' services.slug %}" class="service__title-3">{{ services.title }} <br>{{ services.subtitle }} </a></h3>
                    <div class="service__content-3">
                      <p>{{ services.short_des }}
                      </p>
                      <ul class="">
                        <li>+ {{ services.feature_1 }}</li>
                        <li>+ {{ services.feature_2 }}</li>
                        <li>+ {{ services.feature_3 }}</li>
                      </ul>
                    </div>
                    <div class="service__btn-3">
                      <div class="btn_wrapper">
                        <a href="{% url 'services_details' services.slug %}" class="wc-btn-black btn-hover btn-item"><span></span> Read More
                          <i class="fa-solid fa-arrow-right"></i></a>
                      </div>
                    </div>

                    <div class="service__hover-3" style="background-image: url({{ services.image.url }});"></div>
                  </div>

                  {% endfor %}



                  <div class="service3__img-wrap">
                    <div class="service3__img"></div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </section>
        <!-- Service area end -->


      </main>
          <hr style="margin: 0;">

          <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "About Us",
      "url": "{{ request.scheme }}://{{ request.get_host }}/about/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/about/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Products",
          "url": "{{ request.scheme }}://{{ request.get_host }}/products/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/products/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>


      {% endblock %}