{% extends 'include/base.html' %}


{% block head %}

<title>{{ seo_title }}</title>
<meta name="description" content="{{ seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ seo_title }}">
<meta property="og:description" content="{{ seo_des }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ seo_title }}">
<meta name="twitter:description" content="{{ seo_des }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}


{% block content %}
{% load static %}


  <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>

      <!-- Career area start -->
      <section class="job__area pt-130 pb-150" id="job_list" style="margin-top: 75px; padding-top: 50px;">
        <div class="container">
          <div class="row">
            <div class="col-xxl-12">
              <div class="sec-title-wrapper">
                <h2 class="sec-title title-anim">We’re Currently <br>hiring</h2>
              </div>
            </div>
            <div class="col-xxl-12">
              <div class="job__list">

                {% for career in career %}
                <a href="{% url 'career_details' career.slug %}">
                  <div class="job__item">
                    <p class="job__no">{{ career.order_no }}</p>
                    <h3 class="job__title">{{ career.title }}</h3>
                    <h4 class="job__open">({{ career.role_count }} Open Roles)</h4>
                    <div class="job__link"><span><i class="fa-solid fa-arrow-right"></i></span></div>
                  </div>
                </a>
                {% endfor %}

              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- Career area end -->

      <hr style="margin: 0;">

  <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Products",
      "url": "{{ request.scheme }}://{{ request.get_host }}/products/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/products/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Services",
          "url": "{{ request.scheme }}://{{ request.get_host }}/services/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/services/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>

      {% endblock %}