{% extends 'include/base.html' %}


{% block head %}

<title>{{ seo_title }}</title>
<meta name="description" content="{{ seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ seo_title }}">
<meta property="og:description" content="{{ seo_des }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ seo_title }}">
<meta name="twitter:description" content="{{ seo_des }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}



{% block content %}
{% load static %}



  <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>

      <!-- Contact area start -->
      <section class="contact__area-6" style="background-color: #f6f6f6;">
        <div class="container g-0 pt-120 pb-110">
          <div class="row">
            <div class="col-xxl-6 col-xl-6 col-lg-6 col-md-6">
              <div class="sec-title-wrapper">
                <h2 class="sec-title-2 animation__char_come contact-mobile">Let’s get in touch</h2>
              </div>
            </div>
            <div class="col-xxl-6 col-xl-6 col-lg-6 col-md-6">
              <div class="contact__text">
                <p>Great! We're excited to hear from you and let's start
                  something special togerter. call us for any inquery.</p>
              </div>
            </div>
          </div>
          <div class="row contact__btm">
            <div class="col-xxl-5 col-xl-5 col-lg-5 col-md-5">
              <div class="contact__info">
                <h3 class="sub-title-anim-top animation__word_come">Don't be afraid man ! <br>say hello</h3>
                <ul>
                  <li style="list-style:none;"><a href="tel:+97148520272">+971 4 852 0272</a></li>
                  <li style="list-style:none;"><a href="mailto:<EMAIL>"><EMAIL></a></li>
                  <li style="list-style:none;"><span>Al Kazim Building 3, Abu Hail, Deira,<br>Dubai, UAE.</span></li>
                </ul>
              </div>
            </div>
            <div class="col-xxl-7 col-xl-7 col-lg-7 col-md-7">
              <div class="contact__form">
                <form method="POST">
                  {% csrf_token %}
                  <div class="row g-3">
                    <div class="col-xxl-6 col-xl-6 col-12">
                      <input type="text" name="name" placeholder="Name *">
                    </div>
                    <div class="col-xxl-6 col-xl-6 col-12">
                      <input type="email" name="email" placeholder="Email *">
                    </div>
                  </div>
                  <div class="row g-3">
                    <div class="col-xxl-6 col-xl-6 col-12">
                      <input type="tel" name="phone" placeholder="Phone">
                    </div>
                    <div class="col-xxl-6 col-xl-6 col-12">
                      <input type="text" name="subject" placeholder="Subject *">
                    </div>
                  </div>
                  <div class="row g-3">
                    <div class="col-12">
                      <textarea name="message" placeholder="Messages *"></textarea>
                    </div>
                  </div>
                  <div class="row g-3">
                    <div class="col-12">
                      <div class="btn_wrapper">
                        <button class="wc-btn-primary btn-hover btn-item" type="submit"><span></span> Send <br>Messages <i
                            class="fa-solid fa-arrow-right"></i></button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- Contact area end -->

          {% for message in messages %}

<script>
     window.onload = function() {
     alert('{{message}}')
     }
</script>
                                {% endfor %}

          <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

         <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "About Us",
      "url": "{{ request.scheme }}://{{ request.get_host }}/about/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/about/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Products",
      "url": "{{ request.scheme }}://{{ request.get_host }}/products/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/products/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Services",
          "url": "{{ request.scheme }}://{{ request.get_host }}/services/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/services/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>




      {% endblock %}