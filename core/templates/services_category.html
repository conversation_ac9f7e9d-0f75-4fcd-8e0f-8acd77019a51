{% extends 'include/base.html' %}

{% block head %}

<title>{{ seo_title }}</title>
<meta name="description" content="{{ seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ seo_title }}">
<meta property="og:description" content="{{ seo_des }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ seo_title }}">
<meta name="twitter:description" content="{{ seo_des }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}


{% block content %}
{% load static %}


 <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>


        <!-- Service area start -->
        <section class="service__area service-v2 pt-110 pb-150">
            <div class="container">
              <div class="row">
                <div class="col-xxl-5 col-xl-5 col-lg-5 col-md-5">
                  <div class="sec-title-wrapper wrap">
                    <h2 class="sec-sub-title title-anim">services</h2>
                    <h3 class="sec-title title-anim">Our Extensive <br> Service Portfolio</h3>
                  </div>
                </div>
                <div class="col-xxl-7 col-xl-7 col-lg-7 col-md-7">
                  <div class="service__top-text text-anim">
                    <p>With every single one of our clients we bring forth a deep passion
                      for <span>creative problem solving
                        innovations</span> forward thinking
                      brands boundaries</p>
                  </div>
                </div>
              </div>

              <div class="service__list-wrapper">
                <div class="row">
                  <div class="col-xxl-4 col-xl-4 col-lg-0 col-md-0">
                    <div class="service__img-wrapper">
                        {% for servicecategory in servicecategory %}
                      <img src="{{ servicecategory.image.url }}" alt="{{ servicecategory.alt_title }}" class="service__img img-{{ servicecategory.order_no }} active">
                       {% endfor %}

                    </div>
                  </div>
                  <div class="col-xxl-8 col-xl-8 col-lg-12 col-md-12">
                    <div class="service__list">

                      {% for servicecategory in servicecategory %}

                      <a href="{% url 'services' servicecategory.slug %}">
                        <div class="service__item animation_home1_service" data-service="{{ servicecategory.order_no }}">
                          <div class="service__number"><span>{{ servicecategory.order_no }}</span></div>
                          <div class="service__title-wrapper">
                            <h4 class="service__title">{{ servicecategory.title }}
                          </h4>
                          </div>
                          <div class="service__text">
                            <p>{{ servicecategory.description }}</p>
                          </div>
                          <div class="service__link">
                            <p><i class="fa-solid fa-arrow-right"></i></p>
                          </div>
                        </div>
                      </a>
                      {% endfor %}


                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <!-- Service area end -->
          <hr style="margin: 0;">

          <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "About Us",
      "url": "{{ request.scheme }}://{{ request.get_host }}/about/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/about/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Products",
          "url": "{{ request.scheme }}://{{ request.get_host }}/products/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/products/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>


      {% endblock %}