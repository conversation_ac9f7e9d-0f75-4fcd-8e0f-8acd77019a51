{% extends 'include/base.html' %}


{% block head %}

<title>{{ seo_title }}</title>
<meta name="description" content="{{ seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ seo_title }}">
<meta property="og:description" content="{{ seo_des }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ seo_title }}">
<meta name="twitter:description" content="{{ seo_des }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}



{% block content %}
{% load static %}


 <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>

       <center>
        <div class="col-xxl-12">
          <div class="testimonial__sec-title text-anim" style="background-color: #f6f6f6;">
            <h2 class="sec-subtile-6" style="margin-top: 53px;">PORTFOLIO</h2>
            <h3 class="sec-title-6 title-anim">LATEST WORKS</h3>
            <p>Read what our clients have to say about our services</p>
          </div>
        </div>
      </center>



        <!-- show case -->
        <div class="showcase__area-4" style="background-color: #f6f6f6; padding-bottom: 0; margin-bottom: -39px;">
          <div class="row">

            {% for rowone in rowone %}
            <div class="col-xxl-6 col-xl-6 col-lg-6 col-md-6">
              <div class="showcase4 wc-tilt-2 fade_bottom_3">
                <a href="">
                  <div class="showcase4__thumb">
                    <img src="{{ rowone.image.url }}" alt="{{ rowone.alt_title }}" data-speed="auto">
                  </div>
                </a>
              </div>
            </div>
            {% endfor %}

                        {% for rowtwo in rowtwo %}

            <div class="col-xxl-12 col-xl-12 col-lg-12 col-md-12">
              <div class="showcase4 wc-tilt-2 fade_bottom_3">
                <a href="">
                  <div class="showcase4__thumb">
                    <img src="{{ rowtwo.image.url }}" alt="{{ rowone.alt_title }}" data-speed="auto">
                  </div>
                </a>
              </div>
            </div>

            {% endfor %}



          </div>
        </div>
        <!-- /show case -->


          <hr style="margin: 0;">

          <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "About Us",
      "url": "{{ request.scheme }}://{{ request.get_host }}/about/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/about/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Services",
          "url": "{{ request.scheme }}://{{ request.get_host }}/services/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/services/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>


      {% endblock %}