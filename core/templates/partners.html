{% extends 'include/base.html' %}

{% block head %}

<title>{{ seo_title }}</title>
<meta name="description" content="{{ seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{{ seo_title }}">
<meta property="og:description" content="{{ seo_des }}">
<meta property="og:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{{ seo_title }}">
<meta name="twitter:description" content="{{ seo_des }}">
<meta name="twitter:image" content="{{ request.scheme }}://{{ request.get_host }}/static/assets/images/og.webp">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}




{% block content %}
{% load static %}


 <div id="smooth-wrapper">
    <div id="smooth-content">
      <main>
       <!-- show case -->
       <section class="showcase__area-4 mt-60 pb-120" style="background-color: #f6f6f6;">
        <div class="container">
          <div class="row">

            <div class="col-xxl-12">
                 <center><h2 class="sec-subtile-6" style="margin-top: 28px;">PARTNERS</h2>
                    <h3 class="sec-title-6 title-anim">OUR PARTNERS</h3></center>
                <h2 class="brand__title-3 title-anim">We work with the best in the industry
                </h2>
              <div class="showcase__items-4 showcase-mixitup">

                {% for partners in partners %}
                <div class="showcase__item-4 fade_bottom_2 mix calender posters">
                   {% if partners.link %} <a href="{{ partners.link }}"> {% endif %}
                    <img src="{{ partners.image.url }}" alt="{{ partners.alt_title }}">
                  </a>
                </div>
                {% endfor %}



            </div>
          </div>
        </div>
      </section>
      <!-- /show case -->

            <script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "Home",
      "url": "{{ request.scheme }}://{{ request.get_host }}/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

<script type="application/ld+json">
    {
      "@context": "https://schema.org/",
      "@type": "WebSite",
      "name": "About Us",
      "url": "{{ request.scheme }}://{{ request.get_host }}/about/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ request.scheme }}://{{ request.get_host }}/about/{search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org/",
          "@type": "WebSite",
          "name": "Services",
          "url": "{{ request.scheme }}://{{ request.get_host }}/services/",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/services/{search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>


            <script type="application/ld+json">
                {
                  "@context": "https://schema.org/",
                  "@type": "WebSite",
                  "name": "Contact Us",
                  "url": "{{ request.scheme }}://{{ request.get_host }}/contact/",
                  "potentialAction": {
                    "@type": "SearchAction",
                    "target": "{{ request.scheme }}://{{ request.get_host }}/contact/{search_term_string}",
                    "query-input": "required name=search_term_string"
                  }
                }
    </script>

      {% endblock %}