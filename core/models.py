from ckeditor.fields import Rich<PERSON><PERSON><PERSON><PERSON>ield
from django.db import models
from django_resized import Resized<PERSON><PERSON><PERSON><PERSON>
from ckeditor_uploader.fields import RichTextUploadingField
from django.utils.text import slugify

# Create your models here.

class Product(models.Model):
    order_no = models.IntegerField()
    title = models.CharField(max_length=255)
    subtitle = models.CharField(max_length=255)
    category = models.CharField(max_length=255)
    link = models.URLField()
    image = models.FileField(upload_to='Product')
    alt_title = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.title

class ServiceCategory(models.Model):
    order_no = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    image = models.FileField(upload_to='ServiceCategory')
    description = models.TextField()
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    updated_date = models.DateField(auto_now=True, blank=True, null=True)
    seo_title = models.TextField(blank=True, null=True)
    alt_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.title.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)


class Services(models.Model):
    category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    subtitle = models.CharField(max_length=255)
    image = models.FileField(upload_to='Services')
    big_image = models.FileField(upload_to='Services')
    small_image = models.FileField(upload_to='Services')
    short_des = models.CharField(max_length=300)
    description = models.TextField()
    feature_1 = models.CharField(max_length=255)
    feature_2 = models.CharField(max_length=255)
    feature_3 = models.CharField(max_length=255)
    feature_4 = models.CharField(max_length=255)
    alt_title = models.TextField(blank=True, null=True)
    seo_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)
    updated_date = models.DateField(auto_now=True, blank=True, null=True)


    def __str__(self):
        return self.title


    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.title.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)

class Partner(models.Model):
    title = models.CharField(max_length=255)
    image = models.FileField(upload_to='Partner')
    link = models.URLField(null=True, blank=True)
    alt_title = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.title

class Blog(models.Model):
    PR_STATUS = [
        ('BLOG', 'BLOG'),
        ('PR RELEASE', 'PR RELEASE')
    ]
    name = models.CharField(max_length=255)
    image = models.FileField(upload_to='Blogs')
    category = models.CharField(max_length=255)
    description = RichTextUploadingField()
    type = models.CharField(max_length=50, choices=PR_STATUS)
    post_date = models.DateField()
    viewed_count = models.PositiveIntegerField(default=0, blank=True, null=True)  # Field to store the viewed count
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation
    updated_date = models.DateField(auto_now=True, blank=True, null=True)
    seo_title = models.TextField(blank=True, null=True)
    alt_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.name.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)


class Portfolio(models.Model):
    CHOICES = {
        ('ROW ONE', 'ROW ONE'),
        ('ROW TWO', 'ROW TWO'),
        ('HOME', 'HOME'),
    }
    image = models.FileField(upload_to='Portfolio')
    area = models.CharField(max_length=255, choices=CHOICES)
    alt_title = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.area

class Career(models.Model):
    order_no = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    role_count = models.CharField(max_length=255)
    location = models.CharField(max_length=255)
    date = models.DateField()
    job_type = models.CharField(max_length=255)
    experience = models.CharField(max_length=255)
    salary = models.CharField(max_length=255)
    deadline = models.DateField(null=True, blank=True)
    description = RichTextField()
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation
    updated_date = models.DateField(auto_now=True, blank=True, null=True)
    seo_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.title.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)



class ApplyJobs(models.Model):
    career = models.ForeignKey(Career, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    phone = models.CharField(max_length=255)
    cv = models.FileField(upload_to='Cv')

    def __str__(self):
        return self.name


class Contact(models.Model):
    name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    phone = models.CharField(max_length=255)
    subject = models.CharField(max_length=500)
    message = models.TextField()

    def __str__(self):
        return self.name


class Testimonial(models.Model):
    purpose = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    company = models.CharField(max_length=255)
    position = models.CharField(max_length=255)
    review = models.CharField(max_length=500)
    image = models.FileField(upload_to='Testimonial')
    alt_title = models.TextField(blank=True, null=True)
    def __str__(self):
        return self.name

class SubscribeNewsletter(models.Model):
    email = models.EmailField()
    created = models.DateField(auto_now=True)

    def __str__(self):
        return self.email

class MagazineCategory(models.Model):
    title = models.CharField(max_length=255)
    subtitle = models.CharField(max_length=255)
    image = models.FileField(upload_to='Magazines')
    link = models.URLField(max_length=255)
    description = models.TextField()
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    updated_date = models.DateField(auto_now=True, blank=True, null=True)
    seo_title = models.TextField(blank=True, null=True)
    alt_title = models.TextField(blank=True, null=True)
    seo_description = models.TextField(blank=True, null=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.title.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)


    def __str__(self):
        return self.title

class Magazines(models.Model):
    order_no = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    image = models.FileField(upload_to='Magazines')
    link = models.URLField()
    magazine_category = models.ForeignKey(MagazineCategory, on_delete=models.CASCADE)

    def __str__(self):
        return self.name
