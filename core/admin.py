from django.contrib import admin
from import_export.admin import ImportExportMixin

from core.models import Product, Partner, Blog, Portfolio, Career, ServiceCategory, Services, ApplyJobs, Contact, Testimonial, SubscribeNewsletter, MagazineCategory, Magazines


# Register your models here.


class PartnerAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('title', 'image', 'link')

class BlogAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'image', 'category', 'post_date')



class ProductAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('title', 'subtitle', 'category', 'link')

class PortfolioAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('area', 'image')

class CareerAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('order_no', 'title', 'role_count', 'location', 'date', 'job_type')


class ServiceCategoryAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('order_no', 'title', 'image')

class ServicesAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('category', 'title', 'subtitle', 'image')


class ApplyJobsAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('career', 'name', 'email', 'phone', 'cv')

class ContactAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'email', 'phone', 'subject', 'message')

class TestimonialAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('purpose', 'name', 'image',  'company', 'position', 'review')

class SubscribeNewsletterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('email', 'created')

admin.site.register(MagazineCategory)
admin.site.register(Magazines)
admin.site.register(Partner, PartnerAdmin)
admin.site.register(Blog, BlogAdmin)
admin.site.register(Product, ProductAdmin)
admin.site.register(Portfolio, PortfolioAdmin)
admin.site.register(Career, CareerAdmin)
admin.site.register(ServiceCategory, ServiceCategoryAdmin)
admin.site.register(Services, ServicesAdmin)
admin.site.register(ApplyJobs, ApplyJobsAdmin)
admin.site.register(Contact, ContactAdmin)
admin.site.register(Testimonial, TestimonialAdmin)
admin.site.register(SubscribeNewsletter, SubscribeNewsletterAdmin)






