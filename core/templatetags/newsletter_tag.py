from django import template
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.http import request
from django.shortcuts import redirect

from core.models import SubscribeNewsletter




register = template.Library()


@register.inclusion_tag('template_tags/newsletter_form.html', takes_context=True)
def newsletter_tag(context):
    request = context['request']
    if request.method == 'POST':
        email = request.POST.get('email', None)
        if not email:
            messages.error(request, "You Must Type email to subscribe to a newsletter")
            return redirect("/")
        if get_user_model().objects.filter(email=email).first():
            messages.error(request, "Found registered user with associated email. you must login to subscribe or unsubscribe.")
            return redirect(request.META.get('HTTP_REFERER', '/'))
        subscribe_user = SubscribeNewsletter.objects.filter(email=email).first()
        if subscribe_user:
            messages.error(request, "email address is already subscriber")
            return redirect(request.META.get('HTTP_REFERER', '/'))

        try:
            validate_email(email)
        except ValidationError as e:
            messages.error(request, e.messages[0])
            return redirect('/')

        subscribe_model_instance = SubscribeNewsletter()
        subscribe_model_instance.email = email
        subscribe_model_instance.save()
        messages.success(request, f"(email) email was successfully subscribed to our newsletter")
        return redirect(request.META.get('HTTP_REFERER', '/'))

@register.inclusion_tag('template_tags/base_schema.html', takes_context=True)
def base_schema(context):
    request = context.get('request')  # Extract the request object from the context

    if request:
        current_domain = request.get_host()
        if current_domain == 'gulftimemedia.ae':
            base_url = 'https://gulftimemedia.ae'
        else:
            base_url = 'https://gulftimemedia.com'
        absolute_uri = base_url + request.path_info

        return {'request': request, 'absolute_uri': absolute_uri}

    # Handle the case where the request object is not present in the context
    return {'request': None, 'absolute_uri': None}