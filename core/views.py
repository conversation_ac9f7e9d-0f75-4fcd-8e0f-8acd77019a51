from django.contrib import messages
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from datetime import datetime
from core.forms import ApplyJobForm, ContactForm, NewsletterForm
from core.models import Product, Partner, Blog, Portfolio, Career, ServiceCategory, Services,SubscribeNewsletter, Testimonial, MagazineCategory, Magazines
from django.core.paginator import Paginator #paginator import

def home(request):
    blogs = Blog.objects.filter(type='BLOG')
    partners = Partner.objects.all()
    products = Product.objects.all()
    servicecategory = ServiceCategory.objects.all()
    service = Services.objects.all()

    testimonial = Testimonial.objects.all()
    portfolio = Portfolio.objects.filter(area='HOME')

    current_domain = request.get_host()
    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Best Media Services Digital Marketing & PR Company in Dubai'
        seo_des = 'Gulf Time Media is Dubai best source for media, digital marketing & PR services, can help your company succeed on TV, mobile, web, audio, print, etc.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Best Digital Marketing, PR & Brand Media Marketing Agency in UAE'
        seo_des = 'Gulf Time Media - best leading Digital Marketing, PR & Media Marketing Agency in UAE. We provides best services in SEO, SMM, PPC, Content Marketing, etc.'
    absolute_uri = base_url + request.path

    context = {
        'partners':partners,
        'blogs':blogs,
        'products':products,
        'servicecategory':servicecategory,
        'portfolio':portfolio,
        'testimonial':testimonial,
        'service':service,
        'absolute_uri': absolute_uri,
        'seo_title':seo_title,
        'seo_des':seo_des,
    }
    return render(request, 'home.html', context)

def about(request):
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Best Digital Marketing, PR & Publishing | Media Agency in Dubai'
        seo_des = 'We are one of the Dubai leading digital media & publishing agency. We deliver top-quality services tailored to your unique needs. Explore our services.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Best Online Marketing, Public Relation & Branding Agency in UAE'
        seo_des = 'We are UAE top Online Marketing, PR & Branding Agency. We also provides the best Digital Marketing, PR, Magazine Publication, Content Marketing, etc.'

    absolute_uri = base_url + request.path_info

    context = {
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'about.html', context)


def services_category(request):
    servicecategory = ServiceCategory.objects.all()
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Digital Marketing, PR, Magazine Publishing in Dubai | Services'
        seo_des = 'Gulf Time Media is one of the best Digital Media Company in Dubai. We offers digital, entertainment, publishing & business consultancy services, etc.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Digital Marketing, PR, Publishing, Business Consultancy Services'
        seo_des = 'Take your brand to the next level. Join us for a journey filled with Digital Marketing, PR, Magazine Publishing & Business Consultancy expertise in UAE.'

    absolute_uri = base_url + request.path_info
    context = {
        'servicecategory':servicecategory,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'services_category.html', context)


def services(request, slug):
    servicecategory = ServiceCategory.objects.get(slug=slug)
    services = Services.objects.filter(category=servicecategory)
    current_domain = request.get_host()
    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Pr & Digital Marketing Agency - Dubai'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Pr & Digital Marketing Company - UAE'

    absolute_uri = base_url + request.path_info
    context = {
        'servicecategory':servicecategory,
        'services': services,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
    }
    return render(request, 'services.html', context)


def services_details(request, slug):
    current_month = datetime.today().month
    services = Services.objects.get(slug=slug)
    services_url = Services.objects.filter(updated_date__month=current_month).exclude(slug=slug).order_by('-updated_date')[:4]
    current_domain = request.get_host()
    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Pr & Digital Marketing Agency - Dubai'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Pr & Digital Marketing Company - UAE'
    absolute_uri = base_url + request.path_info


    context = {
        'services':services,
        'services_url':services_url,
        'absolute_uri':absolute_uri,
        'seo_title':seo_title,
    }
    return render(request, 'services_details.html', context)



def products(request):
    product = Product.objects.all()
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Discover the Best Digital Media Products in Dubai | Gulf Time Media'
        seo_des = 'Discover our range of digital media products in Dubai, including digital publishing, live events, web development, digital marketing, pr, etc.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Leading Media Products Company in the UAE | Gulf Time Media LLC'
        seo_des = 'Elevate your business with our diverse media solutions – from digital marketing to magazine publishing, we got you covered for success in the UAE.'

    absolute_uri = base_url + request.path_info
    context = {
        'product':product,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'products.html', context)

def portfolio(request):
    rowone = Portfolio.objects.filter(area='ROW ONE')
    rowtwo = Portfolio.objects.filter(area='ROW TWO')
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Explore Our Creative Portfolio | Top Media Company in Dubai'
        seo_des = 'Unlock digital success with our creative media agency in Dubai. Specializing in social media marketing, we empower FMCG & Wondermom. Explore our portfolio.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Gulf Time Media Portfolio | Best Media Agency in UAE'
        seo_des = 'Our portfolio showcases the commitment to excellence in media solutions. Explore our portfolio to see the results we have achieved for our clients in UAE.'

    absolute_uri = base_url + request.path_info
    context = {
        'rowone': rowone,
        'rowtwo':rowtwo,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'portfolio.html', context)

def partners(request):
    partners = Partner.objects.all()
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Top Trusted Partners in Dubai | Company & Brands'
        seo_des = 'Learn more about our prestigious partners who work with Gulf Time Media to achieve success. Become a part of our UAE network of excellence.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Our Trusted Company & Brand Partners in the UAE | Gulf Time Media'
        seo_des = 'Explore collaboration with Gulf Time Media & our trusted UAE partners. Join a network rooted in trust, reliability & shared success for best experience.'

    absolute_uri = base_url + request.path_info
    context = {
        'partners': partners,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'partners.html', context)

def blogs(request):
    blogs = Blog.objects.filter(type='BLOG').order_by('-post_date')
    paginator = Paginator(blogs, 9)  #code for pagination
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    current_page = page_obj.number
    print(current_page)
    start_page = max(current_page - 2, 1)  # Show 2 pages before current page
    end_page = min(current_page + 3, paginator.num_pages + 1)
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Discover Top & Best: Latest News, Top Blogs & Trending Updates Dubai'
        seo_des = 'Dubai News Hub: Stay Updated with Latest Updates, Top Blogs, and Insights. Explore Dubai vibrant scene with the latest news, top blogs, and insightful updates. '
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Explore Best UAE Latest News, Top Blogs & Trending Updates 2024'
        seo_des = 'Stay informed on UAE latest top & best news, trends, and insights. Explore diverse blogs covering business, culture, technology, and lifestyle.'

    absolute_uri = base_url + request.path_info
    context = {
        'blogs':blogs,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
        'page_obj': page_obj,
        'start_page': start_page,
        'end_page': end_page
    }
    return render(request, 'blog/blogs.html', context)


def blog_details(request, slug):
    current_month = datetime.today().month
    blogs = get_object_or_404(Blog, slug=slug)

    blog_urls = Blog.objects.filter(post_date__month=current_month).exclude(slug=slug).order_by(
        '-post_date')[:4]

    current_domain = request.get_host()
    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Pr & Digital Marketing Agency - Dubai'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Pr & Digital Marketing Company - UAE'
    absolute_uri = base_url + request.path_info

    blogs.viewed_count += 1
    blogs.save()

    context = {
        'blogs': blogs,
        'blog_urls': blog_urls,
        'base_url':base_url,
        'absolute_uri': absolute_uri,
        'seo_title':seo_title,
    }

    return render(request, 'blog/blog_details.html', context)

def pr_release(request):
    pr_release = Blog.objects.filter(type='PR RELEASE').order_by('-post_date')
    paginator = Paginator(pr_release, 9)  #code for pagination
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    current_page = page_obj.number
    print(current_page)
    start_page = max(current_page - 2, 1)  # Show 2 pages before current page
    end_page = min(current_page + 3, paginator.num_pages + 1)

    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Latest Press Releases, Announcements and Updates in Dubai'
        seo_des = 'Stay updated with our latest press releases and media announcements for insights into our innovative services and achievements in the Dubai.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Breaking News & Press Releases: Leading Media Agency in the UAE'
        seo_des = 'Latest news & press releases curated by the UAE best media agency. Your source for timely & relevant updates in digital marketing, pr, publication, etc.'

    absolute_uri = base_url + request.path_info
    context = {
        'pr_release':pr_release,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
        'page_obj': page_obj,
        'start_page': start_page,
        'end_page': end_page
    }
    return render(request, 'pr/pr_release.html', context)

def pr_release_details(request, slug):
    pr_release = get_object_or_404(Blog, slug=slug)
    current_month = datetime.today().month
    blog_urls = Blog.objects.filter(post_date__month=current_month).exclude(slug=slug).order_by(
        '-post_date')[:4]
    pr_release.viewed_count += 1
    pr_release.save()

    current_domain = request.get_host()
    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Pr & Digital Marketing Agency - Dubai'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Pr & Digital Marketing Company - UAE'
    absolute_uri = base_url + request.path_info

    context = {
        'pr_release':pr_release,
        'blog_urls':blog_urls,
        'absolute_uri': absolute_uri,
        'seo_title':seo_title,
    }
    return render(request, 'pr/pr_release_details.html', context)

def career(request):
    career = Career.objects.all()
    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Careers | Top Digital Media Company & Business Consultancy in Dubai'
        seo_des = 'Discover lucrative career opportunities with high salaries at the forefront of digital media. Join a top business consultancy in Dubai for rewarding media careers.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Top & Best Media Agency Jobs in the UAE for High-Paying Careers'
        seo_des = 'Explore top jobs in media agencies. Discover the best careers with high salaries. Jobs in digital marketing, pr, magazine publication, productions, web development, etc.'

    absolute_uri = base_url + request.path_info
    context = {
        'career': career,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'career.html', context)



def magazines(request):
    magazine = MagazineCategory.objects.all()

    context = {
        'magazine':magazine,
    }
    return render(request, 'magazine/magazine.html', context)

def magazines_details(request, slug):
    magazine = MagazineCategory.objects.get(slug=slug)
    magazines = Magazines.objects.filter(magazine_category=magazine)
    context = {
        'magazines':magazines,
        'magazine':magazine,
    }
    return render(request, 'magazine/magazine_details.html', context)


def career_details(request, slug):
    career = Career.objects.get(slug=slug)
    if request.method == 'POST':
        applyjob = ApplyJobForm(request.POST, request.FILES)
        if applyjob.is_valid():
            applyjob = applyjob.save(commit=False)
            applyjob.career = career
            applyjob.save()
            messages.success(request, 'Thanks For Submit')
        else:
            messages.error(request, 'Failed Your Submition')
    else:
        applyjob = ApplyJobForm()

    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Pr & Digital Marketing Agency - Dubai'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Pr & Digital Marketing Company - UAE'

    absolute_uri = base_url + request.path_info

    context = {
        'career': career,
        'applyjob': applyjob,
        'absolute_uri': absolute_uri,
        'seo_title':seo_title,
    }
    return render(request, 'career_details.html', context)

def contact(request):
    if request.method == 'POST':
        contact = ContactForm(request.POST)
        if contact.is_valid():
            contact.save()
            messages.success(request, 'Thanks For Submit')
        else:
            messages.error(request, 'Failed Submit')
    else:
        contact = ContactForm()

    current_domain = request.get_host()

    if current_domain == 'gulftimemedia.ae':
        base_url = 'https://gulftimemedia.ae'
        seo_title = 'Contact Us | Dubai Top Digital Media Agency for Business Solution'
        seo_des = 'Gulf Time Media is one of Dubai biggest digital media company. We provides the premium media services & expert consultancy for your business. Contact Us.'
    else:
        base_url = 'https://gulftimemedia.com'
        seo_title = 'Contact us | Leading Media Agency in the UAE | Business Partner'
        seo_des = 'Your strategic business partner and the premier digital media agency in the UAE. Elevate your brand with our expertise.'

    absolute_uri = base_url + request.path_info

    context = {
        'contact': contact,
        'absolute_uri': absolute_uri,
        'seo_title': seo_title,
        'seo_des': seo_des,
    }
    return render(request, 'contact.html', context)


def newsletter(request):
    newsletterform = NewsletterForm(request.POST or None)
    if newsletterform.is_valid():
        instance = newsletterform.save(commit=False)

        if SubscribeNewsletter.objects.filter(email=instance.email).exists():
            messages.error(request, 'Sorry This is Email Already Exist', extra_tags='newsletter')
        else:
            instance.save()
            messages.info(request, 'Thanks For Signing Up For The Newsletter! We Will Be in Touch Soon.', extra_tags='newsletter')
    else:
        messages.error(request, 'Please Enter Your Valid Email Address', extra_tags='newsletter')
    context = {
        'newsletterform':newsletterform,
    }
    return render(request, 'template_tags/newsletter_form.html', context)

def error_404(request, exception):
    return render(request, 'not_found.html')

def under_construction(request):
    return render(request, 'under_construction.html')