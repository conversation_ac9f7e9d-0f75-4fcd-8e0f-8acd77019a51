"""
URL configuration for gulftimemedia project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf.urls import handler404
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from core.sitemap import OtherSitemap, ServiceCategorySitemap, BlogSitemap, PrSitemap, CareerSitemap
from django.contrib.sitemaps.views import sitemap
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),
    path('profile', include('staff.urls')),
    path('ckeditor/', include('ckeditor_uploader.urls')),
    path('sitemap.xml', sitemap, {'sitemaps': {'other_sitemap': OtherSitemap}}, name='other_sitemap'),
    path('sitemap-service.xml', sitemap, {'sitemaps': {'service_sitemap': ServiceCategorySitemap}}, name='service_sitemap'),
    path('sitemap-blog.xml', sitemap, {'sitemaps': {'blog_sitemap': BlogSitemap}}, name='blog_sitemap'),
    path('sitemap-pr.xml', sitemap, {'sitemaps': {'pr_sitemap': PrSitemap}}, name='pr_sitemap'),
    path('sitemap-career.xml', sitemap, {'sitemaps': {'career_sitemap': CareerSitemap}}, name='career_sitemap'),
    path('robots.txt', TemplateView.as_view(template_name="robots.txt", content_type="text/plain")),
]

handler404 = 'core.views.error_404'


if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
