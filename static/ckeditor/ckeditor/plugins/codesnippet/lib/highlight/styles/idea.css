/*

Intellij Idea-like styling (c) <PERSON><PERSON> <<EMAIL>>

*/

.hljs {
  display: block; padding: 0.5em;
  color: #000;
  background: #fff;
}

.hljs-subst,
.hljs-title {
  font-weight: normal;
  color: #000;
}

.hljs-comment,
.hljs-template_comment,
.hljs-javadoc,
.diff .hljs-header {
  color: #808080;
  font-style: italic;
}

.hljs-annotation,
.hljs-decorator,
.hljs-preprocessor,
.hljs-pragma,
.hljs-doctype,
.hljs-pi,
.hljs-chunk,
.hljs-shebang,
.apache .hljs-cbracket,
.hljs-prompt,
.http .hljs-title {
  color: #808000;
}

.hljs-tag,
.hljs-pi {
  background: #efefef;
}

.hljs-tag .hljs-title,
.hljs-id,
.hljs-attr_selector,
.hljs-pseudo,
.hljs-literal,
.hljs-keyword,
.hljs-hexcolor,
.css .hljs-function,
.ini .hljs-title,
.css .hljs-class,
.hljs-list .hljs-title,
.clojure .hljs-title,
.nginx .hljs-title,
.tex .hljs-command,
.hljs-request,
.hljs-status {
  font-weight: bold;
  color: #000080;
}

.hljs-attribute,
.hljs-rules .hljs-keyword,
.hljs-number,
.hljs-date,
.hljs-regexp,
.tex .hljs-special {
  font-weight: bold;
  color: #0000ff;
}

.hljs-number,
.hljs-regexp {
  font-weight: normal;
}

.hljs-string,
.hljs-value,
.hljs-filter .hljs-argument,
.css .hljs-function .hljs-params,
.apache .hljs-tag {
  color: #008000;
  font-weight: bold;
}

.hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.hljs-char,
.tex .hljs-formula {
  color: #000;
  background: #d0eded;
  font-style: italic;
}

.hljs-phpdoc,
.hljs-yardoctag,
.hljs-javadoctag {
  text-decoration: underline;
}

.hljs-variable,
.hljs-envvar,
.apache .hljs-sqbracket,
.nginx .hljs-built_in {
  color: #660e7a;
}

.hljs-addition {
  background: #baeeba;
}

.hljs-deletion {
  background: #ffc8bd;
}

.diff .hljs-change {
  background: #bccff9;
}
